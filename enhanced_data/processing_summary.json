{"status": "completed", "total_files": 12, "successful": 0, "failed": 12, "results": [{"status": "error", "message": "'Series' object has no attribute 'mad'"}, {"status": "error", "message": "'Series' object has no attribute 'mad'"}, {"status": "error", "message": "'Series' object has no attribute 'mad'"}, {"status": "error", "message": "'Series' object has no attribute 'mad'"}, {"status": "error", "message": "'Series' object has no attribute 'mad'"}, {"status": "error", "message": "'Series' object has no attribute 'mad'"}, {"status": "error", "message": "'Series' object has no attribute 'mad'"}, {"status": "error", "message": "'Series' object has no attribute 'mad'"}, {"status": "error", "message": "<PERSON><PERSON> set a DataFrame with multiple columns to the single column sequence_valid_pattern"}, {"status": "error", "message": "'Series' object has no attribute 'mad'"}, {"status": "error", "message": "'Series' object has no attribute 'mad'"}, {"status": "error", "message": "'Series' object has no attribute 'mad'"}]}